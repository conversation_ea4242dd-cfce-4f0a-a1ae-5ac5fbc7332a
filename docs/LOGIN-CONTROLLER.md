# Login Controller

As of 08.08.2025, the old login method is copied here. Because this is gonna be refactored and removed all the post-login social media update, except keeping the instagram one. Because now, only the instagram is active. 

This method is copied as a dirty-backup, so that when we re-implement the other social media, we can check what data from them needs to be updated.

```php
public function login(Request $request)
    {
        Session::forget('user_type');
        Session::forget('login_error');

        // Validate the form data
        $this->validate($request, [
            'email'   => 'required|email',
            'password' => 'required|min:8'
        ]);

        if ($user = User::where('email', $request->email)->where('user_type', '!=', 'admin')->first()) {
            $daily_login = UserDailyLogin::where('user_id', $user->id)->where(DB::raw("(STR_TO_DATE(created_at,'%Y-%m-%d'))"), date('Y-m-d'))->count();
            if ($daily_login == 0) {
                UserDailyLogin::create([
                    'user_id' => $user->id
                ]);
            }
            
            if ($user->password != '') {
                $remember_me = $request->has('remember_me') ? true : false;
                if (auth()->attempt(['email' => $request->email, 'password' => $request->password], $remember_me)) {
                    Auth::login($user);
                    if (Session::has('follow_rink_id')) {
                        $request = new Request();
                        $request->rink_id = Session::get('follow_rink_id');
                        $request->status = 1;
                        Session::forget('follow_rink_id');
                        $this->followRink($request);
                    }
                    if ($user->user_type == 'influencer') {
                        $records = SocialConnect::where('user_id', $user->id)->get();
                        foreach ($records as $record) {
                            if ($record->token != null &&  $record->token_secret != null && $record->media == 'twitter') {
                                try {
                                    $userSocial = Socialite::driver('twitter')->userFromTokenAndSecret($record->token, $record->token_secret);
                                    $record->update([
                                        'followers' => @$userSocial['followers_count']
                                    ]);
                                } catch (\Exception $e) {
                                }
                            }
                            if ($record->token != null && $record->media == 'youtube') {
                                try {
                                    $youtube_subscribers = file_get_contents('https://www.googleapis.com/youtube/v3/channels?part=statistics&id=' . $record->token . '&key=' . env('YOUTUBE_API_KEY'));
                                    $youtube_api_response = json_decode($youtube_subscribers, true);
                                    // Log::info(print_r($youtube_api_response['items'][0]['statistics']));

                                    $followers_count = intval($youtube_api_response['items'][0]['statistics']['subscriberCount']);

                                    $record->update([
                                        'followers' => $followers_count
                                    ]);
                                } catch (\Exception $e) {
                                }
                            }

                            if ($record->token != null &&  $record->token_secret != null && $record->media == 'twitch') {

                                try {
                                    $result = SocialKeys::first();
                                    $appId = $result->twitch_app_id;
                                    $secret = $result->twitch_app_secret;

                                    $ch = curl_init();
                                    curl_setopt($ch, CURLOPT_URL, "https://id.twitch.tv/oauth2/token");
                                    curl_setopt($ch, CURLOPT_POST, 1);

                                    // In real life you should use something like:
                                    curl_setopt(
                                        $ch,
                                        CURLOPT_POSTFIELDS,
                                        http_build_query([
                                            'client_id' => $appId,
                                            'client_secret' => $secret,
                                            'grant_type' => 'refresh_token',
                                            'refresh_token' => $record->token_secret
                                        ])
                                    );
                                    // Receive server response ...
                                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                                    $response = curl_exec($ch);
                                    curl_close($ch);
                                    $content = json_decode($response);
                                    $videosApi = 'https://api.twitch.tv/helix/users/follows?to_id=' . $record->token;
                                    $clientId = config('services.twitch.client_id');
                                    $ch = curl_init();

                                    curl_setopt_array($ch, array(
                                        CURLOPT_HTTPHEADER => array(
                                            "Accept: application/vnd.twitchtv.v5+json",
                                            'Client-ID: ' . $clientId,
                                            "Authorization: Bearer " . $content->access_token
                                        ),
                                        CURLOPT_SSL_VERIFYPEER => false,
                                        CURLOPT_RETURNTRANSFER => true,
                                        CURLOPT_URL => $videosApi
                                    ));
                                    $response = curl_exec($ch);
                                    curl_close($ch);
                                    $data = json_decode($response, JSON_PRETTY_PRINT);

                                    if (isset($data['total'])) {
                                        $record->update([
                                            'followers' => $data['total'],
                                            'token_secret' => $content->refresh_token
                                        ]);
                                    }
                                } catch (\Exception $e) {
                                }
                            }

                            if ($record->token != null &&  $record->token_secret != null && $record->media == 'facebook') {
                                try {
                                    $result = SocialKeys::first();
                                    $appId = $result->facebook_app_id;
                                    $secret = $result->facebook_app_secret;
                                    $redirectUri = config('app.url') . $result->facebook_callback_url;
                                    $ch = curl_init();
                                    curl_setopt($ch, CURLOPT_URL, "https://graph.facebook.com/oauth/access_token");
                                    curl_setopt($ch, CURLOPT_POST, 1);
                                    curl_setopt(
                                        $ch,
                                        CURLOPT_POSTFIELDS,
                                        http_build_query([
                                            'grant_type' => 'fb_exchange_token',
                                            'client_id' => $appId,
                                            'client_secret' => $secret,
                                            'fb_exchange_token' => $record->token,
                                        ])
                                    );

                                    // Receive server response ...
                                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                                    $response = curl_exec($ch);
                                    curl_close($ch);
                                    $content = json_decode($response);
                                    $record->update([
                                        'token' => $content->access_token
                                    ]);


                                    $url = "https://graph.facebook.com/" . $record->token_secret . "?fields=id,name,picture,followers_count,link&access_token=" . $content->access_token;
                                    $client = new Client();
                                    $response = $client->request('GET', $url);
                                    $content = $response->getBody()->getContents();
                                    $oAuth = json_decode($content);
                                    $record->update([
                                        'followers' => @$oAuth->followers_count
                                    ]);
                                } catch (\Exception $e) {
                                }
                            }

                            if ($record->token != null &&  $record->token_secret != null && $record->media == 'instagram') {

                                try {

                                    $result = SocialKeys::first();
                                    $appId = $result->instagram_app_id;
                                    $secret = $result->instagram_app_secret;
                                    $redirectUri = config('app.url') . $result->instagram_callback_url;
                                    $url = 'https://graph.facebook.com/v2.3/me/accounts?access_token=' . $record->token;
                                    $ch = curl_init();
                                    CURL_SETOPT($ch, CURLOPT_URL, $url);
                                    CURL_SETOPT($ch, CURLOPT_RETURNTRANSFER, 1);
                                    $json = json_decode(curl_exec($ch));

                                    if (isset($json->data[0]->access_token)) {
                                        $access_token = $json->data[0]->access_token;
                                    } else {
                                        $access_token = $record->token;
                                    }

                                    $record->update([
                                        'token' => $access_token
                                    ]);
                                    $url = "https://graph.facebook.com/" . $record->token_secret . "?fields=id,name,username,profile_picture_url,followers_count&access_token=" . $access_token;
                                    $client = new Client();
                                    $response = $client->request('GET', $url);
                                    $content = $response->getBody()->getContents();
                                    $oAuth = json_decode($content);
                                    $record->update([
                                        'followers' => @$oAuth->followers_count
                                    ]);
                                } catch (\Exception $e) {
                                }
                            }

                            if ($record->token != null &&   $record->media == 'tiktok') {
                                try {
                                    $videosApi = "https://open.tiktokapis.com/v2/user/info/?fields=follower_count";
                                    $ch = curl_init();
                                    curl_setopt_array($ch, array(
                                        CURLOPT_HTTPHEADER => array(
                                            "Authorization: Bearer " . @$record->token
                                        ),
                                        CURLOPT_SSL_VERIFYPEER => false,
                                        CURLOPT_RETURNTRANSFER => true,
                                        CURLOPT_URL => $videosApi
                                    ));
                                    $response = curl_exec($ch);
                                    curl_close($ch);
                                    $oAuth = json_decode($response);
                                    $record->update([
                                        'followers' => $oAuth->data->user->follower_count
                                    ]);
                                } catch (\Exception $e) {
                                    \Log::error('TikTok followers update failed', [
                                        'user_id' => $user->id,
                                        'record_id' => $record->id,
                                        'exception' => $e->getMessage(),
                                    ]);
                                }
                            }
                        }
                    }

                    if (Session::has('redirectURL')) {
                        if ($user->user_type == 'influencer') {
                            if ($user->activate != '2') {
                                $redirect = "/influencer-onboarding";
                            } else {
                                $redirect = "/";
                            }
                        } elseif ($user->user_type == 'customer') {
                            $redirect = "/";
                        } else {
                            $redirect = Session::get('redirectURL');
                            Session::forget('redirectURL');
                        }
                    } elseif ($user->user_type == 'influencer') {
                        if ($user->activate != '2') {
                            $redirect = "/influencer-onboarding";
                        } else {
                            $redirect = "/";
                        }
                    } elseif ($user->user_type == 'customer') {
                        $redirect = "/";
                    } else {
                        $redirect = "/";
                    }

                    return redirect($redirect);
                } else {
                    return back()->with('error', 'Invalid Login Attempt. If you’ve forgotten your password, please use the Forgot Password link.');
                }
            } else {
                return back()->with('error', 'Your account is not activated by the admin.');
            }
        } else {
            return redirect()->back()->with('error', 'Invalid Login Attempt. If you’ve forgotten your password, please use the Forgot Password link.')->withInput($request->only('email', 'remember'));
        }
    }
```