<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\FollowRink;
use App\Models\WishlistRink;
use App\Models\Rink;
use App\Models\SocialConnect;
use App\Models\UserDailyLogin;

use App\Models\Country;
use GuzzleHttp\Client;
use App\Models\SocialKeys;
use Socialite;
use Hash;
use Auth;
use Session;
use DB;
use App\Notifications\sendPasswordLink;


use App\Jobs\NewPasswordLink;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout', 'followRink', 'wishlistRink');
    }

    public function showLoginForm()
    {
        $countries = Country::get();
        return view('auth.login', compact('countries'));
    }
    public function showLoginFormDe()
    {
        $countries = Country::get();
        return view('auth.login-de', compact('countries'));
    }

    public function login(Request $request)
    {
        Session::forget('user_type');
        Session::forget('login_error');

        // Validate the form data
        $this->validate($request, [
            'email'   => 'required|email',
            'password' => 'required|min:8'
        ]);

        if ($user = User::where('email', $request->email)->where('user_type', '!=', 'admin')->first()) {
            $daily_login = UserDailyLogin::where('user_id', $user->id)->where(DB::raw("(STR_TO_DATE(created_at,'%Y-%m-%d'))"), date('Y-m-d'))->count();
            if ($daily_login == 0) {
                UserDailyLogin::create([
                    'user_id' => $user->id
                ]);
            }
            
            if ($user->password != '') {
                $remember_me = $request->has('remember_me') ? true : false;
                if (auth()->attempt(['email' => $request->email, 'password' => $request->password], $remember_me)) {
                    Auth::login($user);
                    if (Session::has('follow_rink_id')) {
                        $request = new Request();
                        $request->rink_id = Session::get('follow_rink_id');
                        $request->status = 1;
                        Session::forget('follow_rink_id');
                        $this->followRink($request);
                    }

                    if ($user->user_type == 'influencer') {
                        $records = SocialConnect::where('user_id', $user->id)->get();

                        foreach ($records as $record) {
                            if ($record->token != null &&  $record->token_secret != null && $record->media == 'instagram') {
                                try {
                                    $result = SocialKeys::first();
                                    $appId = $result->instagram_app_id;
                                    $secret = $result->instagram_app_secret;
                                    $redirectUri = config('app.url') . $result->instagram_callback_url;
                                    $url = 'https://graph.facebook.com/v2.3/me/accounts?access_token=' . $record->token;
                                    $ch = curl_init();
                                    CURL_SETOPT($ch, CURLOPT_URL, $url);
                                    CURL_SETOPT($ch, CURLOPT_RETURNTRANSFER, 1);
                                    $json = json_decode(curl_exec($ch));

                                    if (isset($json->data[0]->access_token)) {
                                        $access_token = $json->data[0]->access_token;
                                    } else {
                                        $access_token = $record->token;
                                    }

                                    $record->update([
                                        'token' => $access_token
                                    ]);
                                    $url = "https://graph.facebook.com/" . $record->token_secret . "?fields=id,name,username,profile_picture_url,followers_count&access_token=" . $access_token;
                                    $client = new Client();
                                    $response = $client->request('GET', $url);
                                    $content = $response->getBody()->getContents();
                                    $oAuth = json_decode($content);
                                    $record->update([
                                        'followers' => @$oAuth->followers_count
                                    ]);
                                } catch (\Exception $e) {
                                    \Log::error('Instagram sync error: ' . $e->getMessage(), [
                                        'user_id' => $user->id,
                                        'record_id' => $record->id,
                                        'exception' => $e
                                    ]);
                                }
                            }
                        }
                    }

                    if (Session::has('redirectURL')) {
                        if ($user->user_type == 'influencer') {
                            if ($user->activate != '2') {
                                $redirect = "/influencer-onboarding";
                            } else {
                                $redirect = "/";
                            }
                        } elseif ($user->user_type == 'customer') {
                            $redirect = "/";
                        } else {
                            $redirect = Session::get('redirectURL');
                            Session::forget('redirectURL');
                        }
                    } elseif ($user->user_type == 'influencer') {
                        if ($user->activate != '2') {
                            $redirect = "/influencer-onboarding";
                        } else {
                            $redirect = "/";
                        }
                    } elseif ($user->user_type == 'customer') {
                        $redirect = "/";
                    } else {
                        $redirect = "/";
                    }

                    return redirect($redirect);
                } else {
                    return back()->with('error', 'Invalid Login Attempt. If you’ve forgotten your password, please use the Forgot Password link.');
                }
            } else {
                return back()->with('error', 'Your account is not activated by the admin.');
            }
        } else {
            return redirect()
                ->back()
                ->with('error', 'Invalid Login Attempt. If you’ve forgotten your password, please use the Forgot Password link.')
                ->withInput($request->only('email', 'remember'));
        }
    }

    public function logout()
    {
        Session::forget('user_type');
        Auth::logout();
        return redirect('/login');
    }

    public function forgotPassword(Request $request)
    {
        $countries = Country::get();
        return view('auth.forgot-password', compact('countries'));
    }

    public function activatePassword($token)
    {

        $password_resets = DB::table('password_resets')->where('token', $token)->first();
        if ($password_resets == null) {
            return redirect('/login')->with(['error' => 'This link is expired.']);
        }

        $user = DB::table('users')
            ->select('*')
            ->where('email', $password_resets->email)
            ->first();
        if ($user == null) {
            return redirect('/login')->with(['error' => 'This link is expired.']);
        }

        return view('auth.activate-password', compact('token'));
    }

    public function forgetPassword(Request $request)
    {
        $request->validate(['email' => ['required', 'email']]);
        if ($user = User::where('email', $request->email)->where('user_type', '!=', 'admin')->first()) {
            if ($user->flag == 1 && $user->password != '') {
                $token = str_random(60);
                $password_reset_user = DB::table('password_resets')
                    ->where('email', $request->email)
                    ->first();
                if ($password_reset_user) {
                    $token_saved = DB::table('password_resets')
                        ->where('email', $password_reset_user->email)
                        ->update([
                            'token' => $token
                        ]);
                } else {
                    $token_saved = DB::table('password_resets')->insert([
                        'email' => $request->email,
                        'token' => $token,
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                }
                if ($token_saved) {
                    dispatch(new NewPasswordLink($user, $token));
                    return back()->with('success', 'Please check your email for password reset instructions.');
                } else {
                    return back()->with('error', 'Your account is deactivated contact to support');
                }
            } else {
                return back()->with('error', 'Your account is not activated by the admin');
            }
        } else {
            return back()->with('error', 'Your account is deactivated contact to support');
        }
    }

    public function updateForgotPassword(Request $request)
    {
        $validation = $this->validate($request, ['password' => 'required|min:8|confirmed', 'password_confirmation' => 'required']);
        $email = DB::table('password_resets')
            ->select('email')
            ->where('token', $request->token)
            ->first();
        if (!isset($email)) {
            return redirect('/login')->with(['error' => 'The password reset link is expired try again later.!']);
        }
        $user = DB::table('users')
            ->select('*')
            ->where('email', $email->email)
            ->first();
        if (!isset($email)) {
            return redirect('/login')->with(['error' => "User doesn't exists please try again later.!"]);
        }
        if ($request->password == $request->password_confirmation) {
            if ($user) {
                $password_updated = DB::table('users')
                    ->where('email', $user->email)
                    ->update(['password' => Hash::make($request->password)]);

                if ($password_updated) {
                    return redirect('/login')->with(['success' => 'Password updated successfully.']);
                } else {
                    return redirect('/login')->with(['error' => 'There is an error while changing the password please try again later.!']);
                }
            }
        } else {
            return back()->with('error', 'Password do not matched with confirm password');
        }
    }
}
